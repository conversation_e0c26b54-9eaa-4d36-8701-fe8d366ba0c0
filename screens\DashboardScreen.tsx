"use client"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useTasks } from "../contexts/TaskContext"

const { width } = Dimensions.get("window")

export default function DashboardScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { availableTasks, myTasks } = useTasks()

  const dashboardData = {
    client: {
      activeProjects: 3,
      completedProjects: 12,
      totalSpent: 2340,
      avgRating: 4.8,
      pendingPayments: 450,
      savedProviders: 8,
    },
    worker: {
      activeProjects: 2,
      completedProjects: 28,
      totalEarned: 5680,
      avgRating: 4.9,
      pendingPayments: 890,
      responseRate: 95,
    },
  }

  const currentData = user?.currentMode === "client" ? dashboardData.client : dashboardData.worker

  const quickActions =
    user?.currentMode === "client"
      ? [
          { title: "Nuevo Proyecto", icon: "➕", screen: "CreateTask", color: colors.primary },
          { title: "Buscar Servicios", icon: "🔍", screen: "Tasks", color: colors.accent },
          { title: "Mis Cotizaciones", icon: "💰", screen: "Quotes", color: colors.success },
          { title: "Historial", icon: "📊", screen: "History", color: colors.warning },
        ]
      : [
          { title: "Ver Trabajos", icon: "👀", screen: "Tasks", color: colors.primary },
          { title: "Crear Cotización", icon: "💰", screen: "CreateQuote", color: colors.success },
          { title: "Mi Calendario", icon: "📅", screen: "Calendar", color: colors.accent },
          { title: "Certificaciones", icon: "🏆", screen: "Certifications", color: colors.warning },
        ]

  const recentActivity = [
    {
      id: 1,
      type: "project_completed",
      title: "Proyecto de plomería completado",
      subtitle: "Cliente: María González",
      time: "Hace 2 horas",
      amount: "+$150",
    },
    {
      id: 2,
      type: "new_message",
      title: "Nuevo mensaje recibido",
      subtitle: "Proyecto: Instalación eléctrica",
      time: "Hace 4 horas",
      amount: null,
    },
    {
      id: 3,
      type: "payment_received",
      title: "Pago procesado",
      subtitle: "Proyecto: Limpieza oficina",
      time: "Ayer",
      amount: "+$200",
    },
  ]

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View>
          <Text style={[styles.greeting, { color: colors.text }]}>Buenos días, {user?.name}</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {user?.currentMode === "client" ? "Panel de Cliente" : "Panel de Proveedor"}
          </Text>
        </View>
        <View style={[styles.modeBadge, { backgroundColor: colors.primary + "15" }]}>
          <Text style={[styles.modeText, { color: colors.primary }]}>
            {user?.currentMode === "client" ? "Cliente" : "Trabajador"}
          </Text>
        </View>
      </View>

      {/* Key Metrics */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Métricas Principales</Text>
        <View style={styles.metricsGrid}>
          <View style={[styles.metricCard, { backgroundColor: colors.background }]}>
            <Text style={[styles.metricNumber, { color: colors.primary }]}>{currentData.activeProjects}</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Proyectos Activos</Text>
          </View>
          <View style={[styles.metricCard, { backgroundColor: colors.background }]}>
            <Text style={[styles.metricNumber, { color: colors.success }]}>{currentData.completedProjects}</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Completados</Text>
          </View>
          <View style={[styles.metricCard, { backgroundColor: colors.background }]}>
            <Text style={[styles.metricNumber, { color: colors.accent }]}>
              ${user?.currentMode === "client" ? currentData.totalSpent : currentData.totalEarned}
            </Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Total Gastado" : "Total Ganado"}
            </Text>
          </View>
          <View style={[styles.metricCard, { backgroundColor: colors.background }]}>
            <Text style={[styles.metricNumber, { color: colors.warning }]}>{currentData.avgRating}</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Calificación</Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Acciones Rápidas</Text>
        <View style={styles.actionsGrid}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.actionCard, { backgroundColor: colors.background, borderColor: colors.border }]}
              onPress={() => navigation.navigate(action.screen)}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.color + "15" }]}>
                <Text style={[styles.actionIconText, { color: action.color }]}>{action.icon}</Text>
              </View>
              <Text style={[styles.actionTitle, { color: colors.text }]}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Financial Overview */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Resumen Financiero</Text>
          <TouchableOpacity onPress={() => navigation.navigate("Billing")}>
            <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver detalles</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.financialCards}>
          <View style={[styles.financialCard, { backgroundColor: colors.background }]}>
            <View style={styles.financialHeader}>
              <Text style={[styles.financialTitle, { color: colors.text }]}>Pagos Pendientes</Text>
              <Text style={[styles.financialAmount, { color: colors.warning }]}>${currentData.pendingPayments}</Text>
            </View>
            <Text style={[styles.financialSubtitle, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Por pagar" : "Por cobrar"}
            </Text>
          </View>

          <View style={[styles.financialCard, { backgroundColor: colors.background }]}>
            <View style={styles.financialHeader}>
              <Text style={[styles.financialTitle, { color: colors.text }]}>
                {user?.currentMode === "client" ? "Proveedores Guardados" : "Tasa de Respuesta"}
              </Text>
              <Text style={[styles.financialAmount, { color: colors.success }]}>
                {user?.currentMode === "client" ? currentData.savedProviders : `${currentData.responseRate}%`}
              </Text>
            </View>
            <Text style={[styles.financialSubtitle, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Contactos confiables" : "Promedio mensual"}
            </Text>
          </View>
        </View>
      </View>

      {/* Recent Activity */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Actividad Reciente</Text>
          <TouchableOpacity onPress={() => navigation.navigate("Activity")}>
            <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver todo</Text>
          </TouchableOpacity>
        </View>

        {recentActivity.map((activity) => (
          <View key={activity.id} style={[styles.activityItem, { borderBottomColor: colors.border }]}>
            <View style={styles.activityContent}>
              <Text style={[styles.activityTitle, { color: colors.text }]}>{activity.title}</Text>
              <Text style={[styles.activitySubtitle, { color: colors.textSecondary }]}>{activity.subtitle}</Text>
              <Text style={[styles.activityTime, { color: colors.textSecondary }]}>{activity.time}</Text>
            </View>
            {activity.amount && (
              <Text style={[styles.activityAmount, { color: colors.success }]}>{activity.amount}</Text>
            )}
          </View>
        ))}
      </View>

      {/* Performance Insights */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Insights de Rendimiento</Text>
        <View style={[styles.insightCard, { backgroundColor: colors.background }]}>
          <View style={styles.insightHeader}>
            <Text style={[styles.insightIcon, { color: colors.primary }]}>📈</Text>
            <Text style={[styles.insightTitle, { color: colors.text }]}>
              {user?.currentMode === "client" ? "Ahorro del Mes" : "Crecimiento del Mes"}
            </Text>
          </View>
          <Text style={[styles.insightDescription, { color: colors.textSecondary }]}>
            {user?.currentMode === "client"
              ? "Has ahorrado $120 comparado con el mes anterior usando proveedores verificados."
              : "Tus ingresos han aumentado 15% este mes. ¡Sigue así!"}
          </Text>
        </View>
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 24,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  modeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  modeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  section: {
    marginHorizontal: 24,
    marginBottom: 24,
    padding: 24,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: "600",
  },
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  metricCard: {
    width: (width - 96) / 2 - 8,
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  metricNumber: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: "center",
    fontWeight: "500",
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  actionCard: {
    width: (width - 96) / 2 - 8,
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  actionIconText: {
    fontSize: 20,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
  financialCards: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  financialCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  financialHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  financialTitle: {
    fontSize: 12,
    fontWeight: "600",
    flex: 1,
  },
  financialAmount: {
    fontSize: 16,
    fontWeight: "700",
  },
  financialSubtitle: {
    fontSize: 10,
  },
  activityItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  activitySubtitle: {
    fontSize: 12,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 10,
  },
  activityAmount: {
    fontSize: 14,
    fontWeight: "700",
  },
  insightCard: {
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  insightHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  insightIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
})
