"use client"

import React from "react"

import { useState } from "react"
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

interface SideMenuProps {
  isVisible: boolean
  onClose: () => void
  navigation: any
}

export default function SideMenu({ isVisible, onClose, navigation }: SideMenuProps) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, switchMode, logout } = useUser()
  const [slideAnim] = useState(new Animated.Value(-300))

  React.useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: isVisible ? 0 : -300,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }, [isVisible])

  const menuItems = [
    {
      section: "Principal",
      items: [
        { title: "Dashboard", icon: "📊", screen: "Home", premium: false },
        { title: "Proyectos", icon: "📋", screen: "Tasks", premium: false },
        { title: "Mensaje<PERSON>", icon: "💬", screen: "Chat", premium: false },
        { title: "Calendario", icon: "📅", screen: "Calendar", premium: true },
      ],
    },
    {
      section: "Herramientas",
      items: [
        { title: "Cotizaciones", icon: "💰", screen: "Quotes", premium: true },
        { title: "Contratos", icon: "📄", screen: "Contracts", premium: true },
        { title: "Facturación", icon: "🧾", screen: "Billing", premium: true },
        { title: "Reportes", icon: "📈", screen: "Reports", premium: true },
      ],
    },
    {
      section: "Crecimiento",
      items: [
        { title: "Objetivos", icon: "🎯", screen: "Goals", premium: false },
        { title: "Asistente IA", icon: "🤖", screen: "TuAmigo", premium: false },
        { title: "Certificaciones", icon: "🏆", screen: "Certifications", premium: true },
        { title: "Networking", icon: "🤝", screen: "Network", premium: true },
      ],
    },
  ]

  const handleModeSwitch = async () => {
    const newMode = user?.currentMode === "client" ? "worker" : "client"
    await switchMode(newMode)
    onClose()
  }

  const navigateToScreen = (screen: string, premium: boolean) => {
    if (premium && !user?.isPremium) {
      navigation.navigate("Premium")
    } else {
      navigation.navigate(screen)
    }
    onClose()
  }

  if (!isVisible) return null

  return (
    <View style={styles.overlay}>
      <TouchableOpacity style={styles.backdrop} onPress={onClose} />
      <Animated.View style={[styles.menu, { backgroundColor: colors.surface, transform: [{ translateX: slideAnim }] }]}>
        <ScrollView style={styles.menuContent}>
          {/* User Profile Section */}
          <View style={[styles.profileSection, { borderBottomColor: colors.border }]}>
            <View style={[styles.avatar, { backgroundColor: colors.primary }]}>
              <Text style={styles.avatarText}>{user?.name?.charAt(0).toUpperCase()}</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: colors.text }]}>{user?.name}</Text>
              <Text style={[styles.profileRole, { color: colors.textSecondary }]}>
                {user?.currentMode === "client" ? "Cliente Profesional" : "Proveedor de Servicios"}
              </Text>
              {user?.isVerified && (
                <View style={[styles.verifiedBadge, { backgroundColor: colors.success + "15" }]}>
                  <Text style={[styles.verifiedText, { color: colors.success }]}>✓ Verificado</Text>
                </View>
              )}
            </View>
          </View>

          {/* Mode Switch */}
          <TouchableOpacity
            style={[styles.modeSwitch, { backgroundColor: colors.primary + "10", borderColor: colors.primary }]}
            onPress={handleModeSwitch}
          >
            <View style={styles.modeSwitchContent}>
              <Text style={[styles.modeSwitchTitle, { color: colors.primary }]}>
                Cambiar a {user?.currentMode === "client" ? "Trabajador" : "Cliente"}
              </Text>
              <Text style={[styles.modeSwitchSubtitle, { color: colors.textSecondary }]}>
                {user?.currentMode === "client" ? "Ofrece servicios" : "Contrata servicios"}
              </Text>
            </View>
            <Text style={[styles.modeSwitchIcon, { color: colors.primary }]}>⇄</Text>
          </TouchableOpacity>

          {/* Menu Sections */}
          {menuItems.map((section, sectionIndex) => (
            <View key={sectionIndex} style={styles.menuSection}>
              <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>{section.section}</Text>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={itemIndex}
                  style={[styles.menuItem, { borderBottomColor: colors.border }]}
                  onPress={() => navigateToScreen(item.screen, item.premium)}
                >
                  <View style={styles.menuItemLeft}>
                    <Text style={styles.menuItemIcon}>{item.icon}</Text>
                    <Text style={[styles.menuItemTitle, { color: colors.text }]}>{item.title}</Text>
                  </View>
                  <View style={styles.menuItemRight}>
                    {item.premium && (
                      <View style={[styles.premiumBadge, { backgroundColor: colors.warning + "15" }]}>
                        <Text style={[styles.premiumText, { color: colors.warning }]}>PRO</Text>
                      </View>
                    )}
                    <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ))}

          {/* Settings Section */}
          <View style={styles.menuSection}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Configuración</Text>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                navigation.navigate("Profile")
                onClose()
              }}
            >
              <View style={styles.menuItemLeft}>
                <Text style={styles.menuItemIcon}>👤</Text>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Mi Perfil</Text>
              </View>
              <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.menuItem, { borderBottomColor: colors.border }]} onPress={toggleTheme}>
              <View style={styles.menuItemLeft}>
                <Text style={styles.menuItemIcon}>{theme === "dark" ? "☀️" : "🌙"}</Text>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>
                  Modo {theme === "dark" ? "Claro" : "Oscuro"}
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                navigation.navigate("Settings")
                onClose()
              }}
            >
              <View style={styles.menuItemLeft}>
                <Text style={styles.menuItemIcon}>⚙️</Text>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Configuración</Text>
              </View>
              <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                navigation.navigate("Support")
                onClose()
              }}
            >
              <View style={styles.menuItemLeft}>
                <Text style={styles.menuItemIcon}>🆘</Text>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Soporte</Text>
              </View>
              <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
            </TouchableOpacity>
          </View>

          {/* Logout */}
          <TouchableOpacity
            style={[styles.logoutButton, { backgroundColor: colors.error + "10", borderColor: colors.error }]}
            onPress={() => {
              logout()
              onClose()
            }}
          >
            <Text style={[styles.logoutText, { color: colors.error }]}>Cerrar Sesión</Text>
          </TouchableOpacity>
        </ScrollView>
      </Animated.View>
    </View>
  )
}

const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  menu: {
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    width: 300,
    shadowColor: "#000",
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  menuContent: {
    flex: 1,
    paddingTop: 60,
  },
  profileSection: {
    flexDirection: "row",
    alignItems: "center",
    padding: 24,
    borderBottomWidth: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "700",
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 14,
    marginBottom: 8,
  },
  verifiedBadge: {
    alignSelf: "flex-start",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    fontSize: 10,
    fontWeight: "600",
  },
  modeSwitch: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  modeSwitchContent: {
    flex: 1,
  },
  modeSwitchTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  modeSwitchSubtitle: {
    fontSize: 12,
  },
  modeSwitchIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  menuSection: {
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  menuItemIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: "center",
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  menuItemRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  premiumBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginRight: 8,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: "700",
  },
  menuItemArrow: {
    fontSize: 18,
    fontWeight: "300",
  },
  logoutButton: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
  },
  logoutText: {
    fontSize: 16,
    fontWeight: "600",
  },
})
